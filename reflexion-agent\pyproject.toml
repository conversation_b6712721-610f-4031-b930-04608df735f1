[project]
name = "reflexion-agent"
version = "0.1.0"
description = ""
authors = [
    {name = "suryam76",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.13,<4.0"
dependencies = [
    "python-dotenv (>=1.1.1,<2.0.0)",
    "black (>=25.1.0,<26.0.0)",
    "isort (>=6.0.1,<7.0.0)",
    "langchain (>=0.3.27,<0.4.0)",
    "langchain-openai (>=0.3.31,<0.4.0)",
    "langchain-anthropic (>=0.3.19,<0.4.0)",
    "langgraph (>=0.6.6,<0.7.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
